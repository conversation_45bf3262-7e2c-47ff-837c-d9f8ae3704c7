# Prevent direct access to uploaded files
Options -Indexes

# Deny access to PHP files in uploads directory
<Files "*.php">
    Order Allow,<PERSON>y
    Deny from all
</Files>

# Allow only specific file types
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
    Order Allow,<PERSON>y
    Allow from all
</FilesMatch>

# Deny access to all other file types
<FilesMatch "^.*$">
    Order Allow,<PERSON>y
    Deny from all
</FilesMatch>

# Override for allowed file types
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>
