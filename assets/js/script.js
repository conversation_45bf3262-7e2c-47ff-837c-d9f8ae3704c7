// TechTruv Custom JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Fade in animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Add fade-in class to elements
    const fadeElements = document.querySelectorAll('.service-card, .value-card, .pricing-card, .testimonial-card');
    fadeElements.forEach(el => {
        el.classList.add('fade-in');
        observer.observe(el);
    });

    // Contact form handling
    const contactForm = document.querySelector('#contact form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const contactMethod = document.getElementById('contact-method').value;
            const project = document.getElementById('project').value;
            
            // Basic validation
            if (!name || !email || !project) {
                alert('Please fill in all required fields.');
                return;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }
            
            // Show success message (in a real application, you would send this to a server)
            alert('Thank you for your message! We\'ll get back to you within 12-24 hours.');
            
            // Reset form
            this.reset();
        });
    }

    // Mobile menu toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            navbarCollapse.classList.toggle('show');
        });
        
        // Close mobile menu when clicking on a link
        const mobileNavLinks = navbarCollapse.querySelectorAll('.nav-link');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                navbarCollapse.classList.remove('show');
            });
        });
    }

    // Pricing card hover effects
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Service card animations
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(5deg)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Testimonial carousel (if needed in future)
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    let currentTestimonial = 0;
    
    // Add active class to first testimonial
    if (testimonialCards.length > 0) {
        testimonialCards[0].classList.add('active');
    }

    // WhatsApp and Email click handlers
    const whatsappLinks = document.querySelectorAll('a[href*="whatsapp"], a[href*="WhatsApp"]');
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    
    whatsappLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // You can customize the WhatsApp message here
            const message = encodeURIComponent('Hi TechTruv! I\'m interested in your services. Can we discuss my project?');
            const whatsappNumber = '1234567890'; // Replace with actual WhatsApp number
            this.href = `https://wa.me/${whatsappNumber}?text=${message}`;
        });
    });

    // Scroll to top functionality
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollToTopBtn.className = 'scroll-to-top btn btn-primary';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: none;
        z-index: 1000;
        border: none;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    `;
    
    document.body.appendChild(scrollToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollToTopBtn.style.display = 'block';
        } else {
            scrollToTopBtn.style.display = 'none';
        }
    });
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Initialize tooltips (if using Bootstrap tooltips)
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Image Upload Functionality
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const filePreview = document.getElementById('filePreview');
    const previewGrid = document.getElementById('previewGrid');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadSuccess = document.getElementById('uploadSuccess');
    const uploadBtn = document.getElementById('uploadBtn');
    const clearBtn = document.getElementById('clearBtn');

    let selectedFiles = [];
    const maxFileSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    if (uploadArea && fileInput) {
        // Drag and drop events
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // Click to upload
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', function() {
            const files = Array.from(this.files);
            handleFiles(files);
        });

        // Upload button click
        if (uploadBtn) {
            uploadBtn.addEventListener('click', function() {
                uploadFiles();
            });
        }

        // Clear button click
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                clearFiles();
            });
        }
    }

    function handleFiles(files) {
        files.forEach(file => {
            if (validateFile(file)) {
                selectedFiles.push(file);
                createFilePreview(file);
            }
        });

        if (selectedFiles.length > 0) {
            filePreview.style.display = 'block';
        }
    }

    function validateFile(file) {
        // Check file type
        if (!allowedTypes.includes(file.type)) {
            alert(`File type not supported: ${file.name}. Please upload JPG, PNG, GIF, PDF, DOC, or DOCX files.`);
            return false;
        }

        // Check file size
        if (file.size > maxFileSize) {
            alert(`File too large: ${file.name}. Maximum size is 10MB.`);
            return false;
        }

        // Check if file already selected
        if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
            alert(`File already selected: ${file.name}`);
            return false;
        }

        return true;
    }

    function createFilePreview(file) {
        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';

        const fileIndex = selectedFiles.length - 1;

        let previewContent = '';

        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = previewItem.querySelector('img');
                if (img) {
                    img.src = e.target.result;
                }
            };
            reader.readAsDataURL(file);

            previewContent = `
                <img src="" alt="Preview">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
                <button class="remove-file" onclick="removeFile(${fileIndex})">
                    <i class="fas fa-times"></i>
                </button>
            `;
        } else {
            const fileIcon = getFileIcon(file.type);
            previewContent = `
                <div class="file-icon">
                    <i class="${fileIcon}"></i>
                </div>
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
                <button class="remove-file" onclick="removeFile(${fileIndex})">
                    <i class="fas fa-times"></i>
                </button>
            `;
        }

        previewItem.innerHTML = previewContent;
        previewGrid.appendChild(previewItem);
    }

    function getFileIcon(fileType) {
        if (fileType === 'application/pdf') {
            return 'fas fa-file-pdf';
        } else if (fileType.includes('word') || fileType.includes('document')) {
            return 'fas fa-file-word';
        } else {
            return 'fas fa-file';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Global function to remove files
    window.removeFile = function(index) {
        selectedFiles.splice(index, 1);
        updatePreview();

        if (selectedFiles.length === 0) {
            filePreview.style.display = 'none';
        }
    };

    function updatePreview() {
        previewGrid.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            createFilePreview(file);
        });
    }

    function clearFiles() {
        selectedFiles = [];
        previewGrid.innerHTML = '';
        filePreview.style.display = 'none';
        fileInput.value = '';
        uploadProgress.style.display = 'none';
        uploadSuccess.style.display = 'none';
    }

    function uploadFiles() {
        if (selectedFiles.length === 0) {
            alert('Please select files to upload.');
            return;
        }

        // Show progress
        uploadProgress.style.display = 'block';
        const progressBar = uploadProgress.querySelector('.progress-bar');

        // Create FormData
        const formData = new FormData();
        selectedFiles.forEach((file, index) => {
            formData.append(`files[]`, file);
        });

        // Upload files to server
        fetch('upload.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            uploadProgress.style.display = 'none';
            if (data.success) {
                uploadSuccess.style.display = 'block';
                filePreview.style.display = 'none';

                // Clear files after successful upload
                setTimeout(() => {
                    clearFiles();
                }, 3000);
            } else {
                alert('Upload failed: ' + data.message);
            }
        })
        .catch(error => {
            uploadProgress.style.display = 'none';
            alert('Upload failed. Please try again.');
            console.error('Upload error:', error);
        });

        // Show progress animation while uploading
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90; // Don't reach 100% until server responds
            progressBar.style.width = progress + '%';
        }, 200);
    }

    // Console log for debugging
    console.log('TechTruv website loaded successfully!');
    console.log('Your Name is Your Legacy - TechTruv');
});
