/* TechTruv Custom Styles */

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-color: #374151;
    --text-muted: #6b7280;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Custom Bootstrap Overrides */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Navigation */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.8);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.hero-buttons .btn {
    margin: 0.5rem;
    padding: 1rem 2rem;
    font-weight: 600;
}

.hero-stats h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Hero Visual Elements */
.hero-visual {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.floating-card:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 50%;
    right: 10%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.floating-card.delay-1 {
    animation-delay: 2s;
}

.floating-card.delay-2 {
    animation-delay: 4s;
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.service-icon i {
    transition: all 0.3s ease;
}

.service-card:hover .service-icon i {
    transform: scale(1.1);
    color: var(--secondary-color) !important;
}

/* Value Cards */
.value-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.value-icon i {
    transition: all 0.3s ease;
}

.value-card:hover .value-icon i {
    transform: scale(1.1);
    color: var(--secondary-color) !important;
}

/* Pricing Cards */
.pricing-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.pricing-card.border-primary {
    border-color: var(--primary-color) !important;
}

.pricing-badge {
    font-size: 0.875rem;
    font-weight: 600;
}

/* Testimonial Cards */
.testimonial-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.stars i {
    font-size: 0.875rem;
}

.author-avatar {
    font-size: 1.25rem;
}

/* Contact Form */
.contact-form {
    border: 1px solid #e5e7eb;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

/* Contact Info */
.contact-item {
    padding: 1.5rem;
    background: white;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.contact-details a {
    color: var(--text-color);
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: var(--primary-color);
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #111827 100%);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .hero-stats {
        margin-top: 2rem !important;
    }
    
    .service-card,
    .value-card,
    .pricing-card,
    .testimonial-card {
        margin-bottom: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .upload-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .upload-actions .btn {
        width: 100%;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Utilities */
.shadow-custom {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.border-radius-custom {
    border-radius: 1rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* Image Upload Styles */
.upload-container {
    border: 2px dashed transparent;
    transition: all 0.3s ease;
}

.upload-area {
    border: 2px dashed var(--text-muted);
    border-radius: 1rem;
    padding: 3rem 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
    background: var(--light-color);
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-style: solid;
    background: rgba(37, 99, 235, 0.1);
}

.upload-icon i {
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon i {
    transform: scale(1.1);
    color: var(--primary-color) !important;
}

.file-preview {
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-item {
    position: relative;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    background: white;
    transition: all 0.3s ease;
}

.preview-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-item img {
    max-width: 100%;
    max-height: 80px;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.preview-item .file-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.preview-item .file-name {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--text-color);
    word-break: break-word;
    margin-bottom: 0.25rem;
}

.preview-item .file-size {
    font-size: 0.7rem;
    color: var(--text-muted);
}

.preview-item .remove-file {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 0.7rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preview-item .remove-file:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.upload-progress .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #f3f4f6;
}

.upload-progress .progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
}

.upload-success .alert {
    border: none;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 1rem;
}

.upload-info {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
}
