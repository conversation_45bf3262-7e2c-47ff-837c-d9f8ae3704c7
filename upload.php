<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
$uploadDir = 'uploads/';
$maxFileSize = 10 * 1024 * 1024; // 10MB
$allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

// Create uploads directory if it doesn't exist
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// Response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Check if files were uploaded
if (!isset($_FILES['files'])) {
    sendResponse(false, 'No files uploaded.');
}

$files = $_FILES['files'];
$uploadedFiles = [];
$errors = [];

// Handle multiple files
$fileCount = count($files['name']);

for ($i = 0; $i < $fileCount; $i++) {
    $fileName = $files['name'][$i];
    $fileTmpName = $files['tmp_name'][$i];
    $fileSize = $files['size'][$i];
    $fileError = $files['error'][$i];
    $fileType = $files['type'][$i];
    
    // Check for upload errors
    if ($fileError !== UPLOAD_ERR_OK) {
        $errors[] = "Error uploading $fileName: Upload error code $fileError";
        continue;
    }
    
    // Validate file type
    if (!in_array($fileType, $allowedTypes)) {
        $errors[] = "File type not allowed for $fileName. Allowed types: JPG, PNG, GIF, PDF, DOC, DOCX";
        continue;
    }
    
    // Validate file size
    if ($fileSize > $maxFileSize) {
        $errors[] = "File $fileName is too large. Maximum size is 10MB.";
        continue;
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $uniqueFileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $uploadPath = $uploadDir . $uniqueFileName;
    
    // Move uploaded file
    if (move_uploaded_file($fileTmpName, $uploadPath)) {
        $uploadedFiles[] = [
            'original_name' => $fileName,
            'saved_name' => $uniqueFileName,
            'size' => $fileSize,
            'type' => $fileType,
            'path' => $uploadPath
        ];
        
        // Log the upload (optional)
        $logEntry = date('Y-m-d H:i:s') . " - Uploaded: $fileName as $uniqueFileName\n";
        file_put_contents('upload_log.txt', $logEntry, FILE_APPEND | LOCK_EX);
        
    } else {
        $errors[] = "Failed to save $fileName to server.";
    }
}

// Send email notification (optional)
if (!empty($uploadedFiles)) {
    $to = '<EMAIL>'; // Replace with your email
    $subject = 'New Files Uploaded - TechTruv';
    $message = "New files have been uploaded to your website:\n\n";
    
    foreach ($uploadedFiles as $file) {
        $message .= "- " . $file['original_name'] . " (" . formatBytes($file['size']) . ")\n";
    }
    
    $message .= "\nTotal files: " . count($uploadedFiles) . "\n";
    $message .= "Upload time: " . date('Y-m-d H:i:s') . "\n";
    
    $headers = 'From: <EMAIL>' . "\r\n" .
               'Reply-To: <EMAIL>' . "\r\n" .
               'X-Mailer: PHP/' . phpversion();
    
    // Uncomment the line below to enable email notifications
    // mail($to, $subject, $message, $headers);
}

// Prepare response
if (!empty($uploadedFiles)) {
    $message = count($uploadedFiles) . ' file(s) uploaded successfully.';
    if (!empty($errors)) {
        $message .= ' Some files had errors: ' . implode(', ', $errors);
    }
    sendResponse(true, $message, $uploadedFiles);
} else {
    sendResponse(false, 'No files were uploaded. Errors: ' . implode(', ', $errors));
}

// Helper function to format file sizes
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
